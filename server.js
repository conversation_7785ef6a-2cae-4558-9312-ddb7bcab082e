const express = require('express');
const path = require('path');
const auth = require('./auth');
const jwt = require('jsonwebtoken');
const cookieParser = require('cookie-parser');

const app = express();

// JWT密钥（生产环境应使用更复杂的密钥并存储在环境变量中）
const JWT_SECRET = 'your-secret-key-here';
const JWT_EXPIRES_IN = '1h'; // Token有效期1小时

// 配置中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname)));

// 生成JWT Token
function generateToken(user) {
    return jwt.sign(
        {
            username: user.username,
            userInfo: user.userInfo
        },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRES_IN }
    );
}

// 验证JWT Token中间件
function authenticateToken(req, res, next) {
    // 从cookie或Authorization头获取token
    const token = req.cookies.token || req.headers.authorization?.split(' ')[1];

    if (!token) {
        // 如果是API请求，返回JSON错误
        if (req.path.startsWith('/api/') || req.xhr) {
            return res.status(401).json({
                success: false,
                message: '访问被拒绝，请先登录'
            });
        }
        // 如果是页面请求，重定向到登录页
        return res.redirect('/login');
    }

    try {
        const verified = jwt.verify(token, JWT_SECRET);
        req.user = verified;
        next();
    } catch (error) {
        // 如果是API请求，返回JSON错误
        if (req.path.startsWith('/api/') || req.xhr) {
            return res.status(403).json({
                success: false,
                message: 'Token无效或已过期'
            });
        }
        // 如果是页面请求，重定向到登录页
        return res.redirect('/login');
    }
}

// 根路径直接显示主页（公开访问）
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 提供登录页面（公开访问）
app.get('/login', (req, res) => {
    // 如果已登录，直接跳转到首页
    const token = req.cookies.token;
    if (token) {
        try {
            jwt.verify(token, JWT_SECRET);
            return res.redirect('/index.html');
        } catch (error) {
            // Token无效，继续显示登录页
        }
    }
    res.sendFile(path.join(__dirname, 'login.html'));
});

// 处理登录请求（公开访问）
app.post('/login', async (req, res) => {
    const { username, password } = req.body;

    try {
        const result = await auth.login(username, password);

        if (result.success) {
            // 生成JWT Token
            const token = generateToken({
                username: result.username,
                userInfo: result.user
            });

            // 设置HTTP-only cookie
            res.cookie('token', token, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production', // 生产环境使用HTTPS
                maxAge: 60 * 60 * 1000 // 1小时
            });

            res.json({
                success: true,
                message: '登录成功',
                token: token,
                username: result.username,
                user: result.user
            });
        } else {
            // 根据错误类型返回不同的状态码
            if (result.message.includes('用户名和密码不能为空')) {
                res.status(400).json(result);
            } else if (result.message.includes('用户名或密码错误')) {
                res.status(401).json(result);
            } else {
                res.status(500).json(result);
            }
        }
    } catch (error) {
        console.error('登录处理错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

// Token刷新接口
app.post('/refresh', (req, res) => {
    const token = req.cookies.token || req.body.token;

    if (!token) {
        return res.status(401).json({
            success: false,
            message: '没有提供Token'
        });
    }

    try {
        // 验证旧Token（忽略过期时间）
        const decoded = jwt.verify(token, JWT_SECRET, { ignoreExpiration: true });

        // 生成新Token
        const newToken = generateToken({
            username: decoded.username,
            userInfo: decoded.userInfo
        });

        // 设置新Cookie
        res.cookie('token', newToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 60 * 60 * 1000
        });

        res.json({
            success: true,
            token: newToken
        });
    } catch (error) {
        console.error('Token刷新失败:', error);
        res.status(403).json({
            success: false,
            message: 'Token无效'
        });
    }
});

// 受保护的主页路由（要求认证）
app.get('/index.html', authenticateToken, (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 登出接口
app.post('/logout', (req, res) => {
    res.clearCookie('token');
    res.json({ success: true, message: '已成功登出' });
});

// 验证Token接口
app.get('/verify', authenticateToken, (req, res) => {
    res.json({
        success: true,
        user: req.user
    });
});

// 单独的用户信息查询接口（要求认证）
app.get('/user/:username', authenticateToken, async (req, res) => {
    const { username } = req.params;

    try {
        // 这里需要密码，实际应用中可能需要调整
        const userInfo = await auth.getUserInfo(username, '');

        if (userInfo) {
            res.json({
                success: true,
                user: userInfo
            });
        } else {
            res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
    } catch (error) {
        console.error('查询用户信息错误:', error);
        res.status(500).json({
            success: false,
            message: '查询用户信息失败'
        });
    }
});

// 健康检查接口（公开访问）
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString()
    });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({
        success: false,
        message: '服务器内部错误'
    });
});

// 404 处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: '页面不存在'
    });
});

// 启动服务器
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
    console.log(`登录页面: http://localhost:${PORT}/login`);
    console.log(`健康检查: http://localhost:${PORT}/health`);
    console.log(`Token刷新接口: http://localhost:${PORT}/refresh`);
});

module.exports = app;