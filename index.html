<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .user-info {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1.5rem;
        }

        .logout-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
        }

        .logout-btn:hover {
            background-color: #c82333;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>欢迎访问系统</h1>
            <button class="logout-btn" id="logoutBtn">退出登录</button>
        </div>

        <div id="userInfo" class="user-info">
            <h3>加载用户信息中...</h3>
        </div>

        <div>
            <h2>系统功能</h2>
            <p>这里是您登录后可以看到的内容。</p>
            <p>只有通过身份验证的用户才能访问此页面。</p>
        </div>
    </div>

    <script>
        // 页面加载时验证Token
        async function verifyAuth() {
            const token = localStorage.getItem('token') || getCookie('token');

            if (!token) {
                // 不弹出alert，直接显示未登录状态
                displayUserInfo(null);
                return;
            }

            try {
                const response = await fetch('/verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayUserInfo(data.user);
                } else {
                    throw new Error('Token验证失败');
                }
            } catch (error) {
                console.error('验证失败:', error);
                // 不弹出alert，直接显示未登录状态
                displayUserInfo(null);
            }
        }

        // 显示用户信息
        function displayUserInfo(user) {
            const userInfoDiv = document.getElementById('userInfo');
            if (user && user.userInfo) {
                userInfoDiv.innerHTML = `
                    <h3>用户信息</h3>
                    <p><strong>用户名:</strong> ${user.username}</p>
                    <p><strong>显示名:</strong> ${user.userInfo.displayName || '未知'}</p>
                    <p><strong>邮箱:</strong> ${user.userInfo.mail || '未知'}</p>
                    <p><strong>部门:</strong> ${user.userInfo.department || '未知'}</p>
                    <p><strong>职位:</strong> ${user.userInfo.title || '未知'}</p>
                `;
            } else {
                userInfoDiv.innerHTML = `<p>欢迎访问！如需查看个人信息，请<a href="/login">登录</a>。</p>`;
            }
        }

        // 获取Cookie的函数
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
        }

        // 登出功能
        document.getElementById('logoutBtn').addEventListener('click', async function () {
            try {
                const response = await fetch('/logout', {
                    method: 'POST'
                });

                if (response.ok) {
                    // 清除本地存储的token
                    localStorage.removeItem('token');
                    window.location.href = '/login';
                }
            } catch (error) {
                console.error('登出失败:', error);
            }
        });

        // 页面加载时验证
        document.addEventListener('DOMContentLoaded', verifyAuth);
    </script>
</body>

</html>