const jwt = require('jsonwebtoken');

// JWT配置
const JWT_SECRET = 'your-secret-key-here';
const JWT_EXPIRES_IN = '1h'; // Token有效期1小时

/**
 * 生成JWT Token
 * @param {Object} user - 用户信息对象
 * @param {string} user.username - 用户名
 * @param {Object} user.userInfo - 用户详细信息
 * @returns {string} - JWT Token字符串
 */
function generateToken(user) {
    return jwt.sign(
        {
            username: user.username,
            userInfo: user.userInfo
        },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRES_IN }
    );
}

/**
 * 验证JWT Token中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function authenticateToken(req, res, next) {
    // 从cookie或Authorization头获取token
    const token = req.cookies.token || req.headers.authorization?.split(' ')[1];

    if (!token) {
        // 如果是API请求，返回JSON错误
        if (req.path.startsWith('/api/') || req.xhr) {
            return res.status(401).json({
                success: false,
                message: '访问被拒绝，请先登录'
            });
        }
        // 如果是页面请求，重定向到登录页
        return res.redirect('/login');
    }

    try {
        const verified = jwt.verify(token, JWT_SECRET);
        req.user = verified;
        next();
    } catch (error) {
        // 如果是API请求，返回JSON错误
        if (req.path.startsWith('/api/') || req.xhr) {
            return res.status(403).json({
                success: false,
                message: 'Token无效或已过期'
            });
        }
        // 如果是页面请求，重定向到登录页
        return res.redirect('/login');
    }
}

/**
 * 验证Token是否有效（不包含中间件逻辑）
 * @param {string} token - JWT Token字符串
 * @param {Object} options - 验证选项
 * @returns {Object|null} - 解码后的token数据或null
 */
function verifyToken(token, options = {}) {
    try {
        return jwt.verify(token, JWT_SECRET, options);
    } catch (error) {
        return null;
    }
}

/**
 * 设置Token Cookie的配置
 * @returns {Object} - Cookie配置对象
 */
function getTokenCookieConfig() {
    return {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production', // 生产环境使用HTTPS
        maxAge: 60 * 60 * 1000 // 1小时
    };
}

/**
 * 刷新Token
 * @param {string} oldToken - 旧的JWT Token
 * @returns {Object} - 包含新token的结果对象
 */
function refreshToken(oldToken) {
    try {
        // 验证旧Token（忽略过期时间）
        const decoded = jwt.verify(oldToken, JWT_SECRET, { ignoreExpiration: true });

        // 生成新Token
        const newToken = generateToken({
            username: decoded.username,
            userInfo: decoded.userInfo
        });

        return {
            success: true,
            token: newToken
        };
    } catch (error) {
        return {
            success: false,
            message: 'Token无效'
        };
    }
}

// 导出模块
module.exports = {
    generateToken,
    authenticateToken,
    verifyToken,
    refreshToken,
    getTokenCookieConfig,
    JWT_SECRET,
    JWT_EXPIRES_IN
};
